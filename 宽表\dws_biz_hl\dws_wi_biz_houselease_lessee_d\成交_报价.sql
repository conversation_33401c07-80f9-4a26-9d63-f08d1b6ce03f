WITH LesseeBase AS (
    -- 基础数据：筛选出意向承租方相关的所有记录
    SELECT
        project_id,
        project_name,
        intnt_rent_id,
        intnt_rent_nm,
        lessee_type, -- 承租方类型
        lessee_province,                               -- 承租方所在省
        lessee_city,                                   -- 承租方所在市
        province_house,
        city_house,
        district_house,
        biz_district_house,
        dt_address_house,
        deal_date,
        deal_price_std_sq_d,
        deal_total_price,
        asset_cate,
        list_price_std_sq_d,
        list_total_price_cal,
        list_price_sep,
        lease_area,
        lease_prd_std_d,
        lessee_name_second_bid,
        lessee_name_third_bid,
        -- 用于判断是否成交或在Top3
        CASE WHEN deal_date IS NOT NULL THEN 1 ELSE 0 END AS is_deal,
        CASE WHEN intnt_rent_nm IN (lessee_name_second_bid, lessee_name_third_bid) THEN 1 ELSE 0 END AS is_top3_bidder_only_rank -- 注意这里只判断竞价排名，不考虑成交
    FROM
        dws_wi_biz_houselease_lessee_d
),
-- 计算历史成交相关指标
LesseeDealHistory AS (
    SELECT
        intnt_rent_id,
        intnt_rent_nm AS intend_lessee_name, -- 意向承租方名称

        -- 是否成交过房屋出租项目
        MAX(CASE WHEN is_deal = 1 THEN 1 ELSE 0 END) AS is_his_house_prj,
        -- 历史成交项目数量
        COUNT(DISTINCT CASE WHEN is_deal = 1 THEN project_id END) AS his_deal_prj_cnt,
        -- 历史平均成交租金价格
        AVG(CASE WHEN is_deal = 1 THEN deal_price_std_sq_d END) AS his_deal_price_avg,
        -- 历史成交租金总额
        SUM(CASE WHEN is_deal = 1 THEN deal_total_price END) AS his_deal_price_total,
        -- 历史成交的房屋类项目数量
        COUNT(DISTINCT CASE WHEN is_deal = 1 AND asset_cate = '房屋' THEN project_id END) AS his_deal_house_prj_cnt,
        -- 历史成交的土地类项目数量
        COUNT(DISTINCT CASE WHEN is_deal = 1 AND asset_cate = '土地' THEN project_id END) AS his_deal_land_prj_cnt,
        -- 历史单项目成交租金价格_最大值
        MAX(CASE WHEN is_deal = 1 THEN deal_price_std_sq_d END) AS his_deal_price_max,
        -- 历史单项目成交租金总额_最大值
        MAX(CASE WHEN is_deal = 1 THEN deal_total_price END) AS his_deal_total_price_max
    FROM
        LesseeBase
    WHERE is_deal = 1  -- 只统计成交记录
    GROUP BY
        intnt_rent_id, intnt_rent_nm
),
-- 计算历史成交项目最多的地区信息
LesseeDealLocationStats AS (
    SELECT
        intnt_rent_id,
        -- 历史成交项目最多的省-市-区
        FIRST_VALUE(district_info) OVER (PARTITION BY intnt_rent_id ORDER BY district_cnt DESC, deal_date DESC) AS his_deal_district_most,
        -- 历史成交项目最多的商圈
        FIRST_VALUE(bizscop_info) OVER (PARTITION BY intnt_rent_id ORDER BY bizscop_cnt DESC, deal_date DESC) AS his_deal_bizscop_most,
        -- 历史成交项目最多的详细地址
        FIRST_VALUE(address_info) OVER (PARTITION BY intnt_rent_id ORDER BY address_cnt DESC, deal_date DESC) AS his_deal_address_most
    FROM (
        SELECT
            intnt_rent_id,
            CONCAT(province_house, '-', city_house, '-', district_house) AS district_info,
            biz_district_house AS bizscop_info,
            dt_address_house AS address_info,
            deal_date,
            COUNT(DISTINCT project_id) OVER (PARTITION BY intnt_rent_id, province_house, city_house, district_house) AS district_cnt,
            COUNT(DISTINCT project_id) OVER (PARTITION BY intnt_rent_id, biz_district_house) AS bizscop_cnt,
            COUNT(DISTINCT project_id) OVER (PARTITION BY intnt_rent_id, dt_address_house) AS address_cnt
        FROM LesseeBase
        WHERE is_deal = 1
    ) t
    QUALIFY ROW_NUMBER() OVER (PARTITION BY intnt_rent_id ORDER BY district_cnt DESC, deal_date DESC) = 1
),
-- 计算最近一次成交相关指标
LesseeLastDeal AS (
    SELECT
        intnt_rent_id,
        project_id AS last_deal_prj_id, -- 最近一次成交项目编号
        project_name AS last_deal_prj_nm, -- 最近一次成交项目名称 
        deal_date AS last_deal_date, -- 最近一次成交日期
        DATEDIFF(CURRENT_DATE(), deal_date) AS last_deal_since_days, -- 最近一次成交距今天数
        asset_cate AS last_deal_asset_cate, -- 最近一次成交项目的资产类型
        deal_price_std_sq_d AS last_deal_price, -- 最近一次成交租金价格
        lease_area AS last_deal_area, -- 最近一次成交的出租面积
        lease_prd_std_d AS last_deal_period, -- 最近一次成交的租赁期
        dt_address_house AS last_deal_location, -- 最近一次成交项目的房屋坐落位置
        CONCAT(province_house, '-', city_house, '-', district_house) AS last_deal_district, -- 最近一次成交项目的房屋所在省-市-区
        biz_district_house AS last_deal_bizscop, -- 最近一次成交项目的房屋所在商圈
        NULL AS last_deal_address, -- 最近一次成交项目的房屋所在楼宇 (设为空值，如有具体楼宇字段请替换)
        ROW_NUMBER() OVER(PARTITION BY intnt_rent_id ORDER BY deal_date DESC) AS rn -- 按成交日期倒序排序，取最新
    FROM
        LesseeBase
    WHERE
        is_deal = 1
),
-- 计算历史报价相关指标
LesseeBidHistory AS (
    SELECT
        intnt_rent_id,
        COUNT(DISTINCT project_id) AS his_bid_prj_cnt, -- 历史报价过的项目数量
        COUNT(DISTINCT CASE WHEN asset_cate = '房屋' THEN project_id END) AS his_bid_house_cate_cnt, -- 历史报价过的房屋类项目数量
        COUNT(DISTINCT CASE WHEN asset_cate = '土地' THEN project_id END) AS his_bid_land_cate_cnt, -- 历史报价过的土地类项目数量

        SUM(CASE WHEN is_deal = 1 THEN 1 ELSE 0 END) AS deal_count, -- 成交次数
        COUNT(project_id) AS bid_count, -- 报价总次数 (这里是针对每个项目意向承租方都有记录)
        SUM(CASE WHEN is_deal = 1 OR is_top3_bidder_only_rank = 1 THEN 1 ELSE 0 END) AS top3_bid_count -- 历史报价在项目总报价中排名前三的次数 (成交或竞价排名在前三)
    FROM
        LesseeBase
    GROUP BY
        intnt_rent_id
),
-- 计算历史报价项目最多的地区信息
LesseeBidLocationStats AS (
    SELECT
        intnt_rent_id,
        -- 历史报价最多的房屋所在省-市-区
        FIRST_VALUE(district_info) OVER (PARTITION BY intnt_rent_id ORDER BY district_cnt DESC, deal_date DESC) AS his_bid_district_most,
        -- 历史报价最多的房屋所在商圈
        FIRST_VALUE(bizscop_info) OVER (PARTITION BY intnt_rent_id ORDER BY bizscop_cnt DESC, deal_date DESC) AS his_bid_bizscop_most
    FROM (
        SELECT
            intnt_rent_id,
            CONCAT(province_house, '-', city_house, '-', district_house) AS district_info,
            biz_district_house AS bizscop_info,
            deal_date,
            COUNT(DISTINCT project_id) OVER (PARTITION BY intnt_rent_id, province_house, city_house, district_house) AS district_cnt,
            COUNT(DISTINCT project_id) OVER (PARTITION BY intnt_rent_id, biz_district_house) AS bizscop_cnt
        FROM LesseeBase
    ) t
    QUALIFY ROW_NUMBER() OVER (PARTITION BY intnt_rent_id ORDER BY district_cnt DESC, deal_date DESC) = 1
),
-- 计算最近一次报价相关指标
LesseeLastBid AS (
    SELECT
        intnt_rent_id,
        deal_date AS last_bid_date, -- 最近一次报价的日期 (使用deal_date，如有专门的报价日期字段请替换)
        project_name AS last_bid_prj_nm, -- 最近一次报价的项目名称 
        project_id AS last_bid_prj_id, -- 最近一次报价的项目编号
        asset_cate AS last_bid_asset_cate, -- 最近一次报价的资产类型
        list_price_std_sq_d AS last_bid_prj_price, -- 最近一次报价的项目挂牌租金价格
        list_total_price_cal AS last_bid_prj_total_price, -- 最近一次报价的项目挂牌租金总额
        CONCAT(province_house, '-', city_house, '-', district_house) AS last_bid_district, -- 最近一次报价的地区_省-市-区
        biz_district_house AS last_bid_bizscop, -- 最近一次报价的地区_商圈
        ROW_NUMBER() OVER(PARTITION BY intnt_rent_id ORDER BY deal_date DESC) AS rn -- 按日期倒序排序，取最新
    FROM
        LesseeBase
),
-- 汇总所有承租方信息
AggregatedLesseeInfo AS (
    SELECT
        -- 基本信息
        lb.intnt_rent_id,
        lb.intnt_rent_nm AS intend_lessee_name, -- 意向承租方名称
        lb.lessee_type AS intend_lessee_type, -- 意向承租方类型
        lb.lessee_province, -- 所在省份
        lb.lessee_city, -- 所在城市
        -- 历史成交相关
        ldh.is_his_house_prj, -- 是否成交过房屋出租项目
        ldh.his_deal_prj_cnt, -- 历史成交项目数量
        ldh.his_deal_price_avg, -- 历史平均成交租金价格
        ldh.his_deal_price_total, -- 历史成交租金总额
        ldh.his_deal_house_prj_cnt, -- 历史成交的房屋类项目数量
        ldh.his_deal_land_prj_cnt, -- 历史成交的土地类项目数量
        ldh.his_deal_price_max, -- 历史单项目成交租金价格_最大值
        ldh.his_deal_total_price_max, -- 历史单项目成交租金总额_最大值
        ldls.his_deal_district_most, -- 历史成交项目最多的省-市-区
        ldls.his_deal_bizscop_most, -- 历史成交项目最多的商圈
        ldls.his_deal_address_most, -- 历史成交项目最多的详细地址
        -- 最近一次成交相关
        lld.last_deal_prj_nm, -- 最近一次成交项目名称
        lld.last_deal_date, -- 最近一次成交日期
        lld.last_deal_since_days, -- 最近一次成交距今天数
        lld.last_deal_asset_cate, -- 最近一次成交项目的资产类型
        lld.last_deal_price, -- 最近一次成交租金价格
        lld.last_deal_area, -- 最近一次成交的出租面积
        lld.last_deal_period, -- 最近一次成交的租赁期
        lld.last_deal_location, -- 最近一次成交项目的房屋坐落位置
        lld.last_deal_district, -- 最近一次成交项目的房屋所在省-市-区
        lld.last_deal_bizscop, -- 最近一次成交项目的房屋所在商圈
        lld.last_deal_address, -- 最近一次成交项目的房屋所在楼宇
        -- 历史报价相关
        lbh.his_bid_prj_cnt, -- 历史报价过的项目数量
        lbh.his_bid_house_cate_cnt, -- 历史报价过的房屋类项目数量
        lbh.his_bid_land_cate_cnt, -- 历史报价过的土地类项目数量
        lbls.his_bid_district_most, -- 历史报价最多的房屋所在省-市-区
        lbls.his_bid_bizscop_most, -- 历史报价最多的房屋所在商圈
        -- 最近一次报价相关
        llb.last_bid_date, -- 最近一次报价的日期
        llb.last_bid_prj_nm, -- 最近一次报价的项目名称
        llb.last_bid_prj_id, -- 最近一次报价的项目编号
        llb.last_bid_asset_cate, -- 最近一次报价的资产类型
        llb.last_bid_prj_price, -- 最近一次报价的租金价格
        llb.last_bid_prj_total_price, -- 最近一次报价的租金总额
        llb.last_bid_district, -- 最近一次报价的地区_省-市-区
        llb.last_bid_bizscop, -- 最近一次报价的地区_商圈

        -- 历史报价成交率 (如果报价总次数为0，则为0)
        COALESCE(lbh.deal_count / NULLIF(lbh.bid_count, 0), 0) AS his_bid_deal_rate,
        lbh.top3_bid_count AS his_bid_top3_num, -- 历史报价在项目总报价中排名前三的次数
        -- 历史报价在项目总报价中排名前三的次数占比 (如果报价总次数为0，则为0)
        COALESCE(lbh.top3_bid_count / NULLIF(lbh.bid_count, 0), 0) AS his_bid_top3_rate
    FROM
        (SELECT DISTINCT intnt_rent_id, intnt_rent_nm,lessee_type,lessee_province,lessee_city FROM LesseeBase) AS lb -- 基本信息
    LEFT JOIN
        LesseeDealHistory AS ldh ON lb.intnt_rent_id = ldh.intnt_rent_id
    LEFT JOIN
        LesseeDealLocationStats AS ldls ON lb.intnt_rent_id = ldls.intnt_rent_id
    LEFT JOIN
        LesseeLastDeal AS lld ON lb.intnt_rent_id = lld.intnt_rent_id AND lld.rn = 1
    LEFT JOIN
        LesseeBidHistory AS lbh ON lb.intnt_rent_id = lbh.intnt_rent_id
    LEFT JOIN
        LesseeBidLocationStats AS lbls ON lb.intnt_rent_id = lbls.intnt_rent_id
    LEFT JOIN
        LesseeLastBid AS llb ON lb.intnt_rent_id = llb.intnt_rent_id AND llb.rn = 1
)
-- 最终查询：将原始项目信息与聚合后的承租方信息联接
SELECT
    t2.intend_lessee_name, -- 意向承租方名称
    t2.intend_lessee_type, -- 意向承租方类型
    t2.lessee_province, -- 所在省份
    t2.lessee_city, -- 所在城市
    t2.is_his_house_prj, -- 是否成交过房屋出租项目
    t2.his_deal_prj_cnt, -- 历史成交项目数量
    t2.his_deal_price_avg, -- 历史平均成交租金价格
    t2.his_deal_price_total, -- 历史成交租金总额
    t2.his_deal_house_prj_cnt, -- 历史成交的房屋类项目数量
    t2.his_deal_land_prj_cnt, -- 历史成交的土地类项目数量
    t2.his_deal_price_max, -- 历史单项目成交租金价格_最大值
    t2.his_deal_total_price_max, -- 历史单项目成交租金总额_最大值
    t2.his_deal_district_most, -- 历史成交项目最多的省-市-区
    t2.his_deal_bizscop_most, -- 历史成交项目最多的商圈
    t2.his_deal_address_most, -- 历史成交项目最多的详细地址
    t2.last_deal_prj_nm, -- 最近一次成交项目名称
    t2.last_deal_date, -- 最近一次成交日期
    t2.last_deal_since_days, -- 最近一次成交距今天数
    t2.last_deal_asset_cate, -- 最近一次成交项目的资产类型
    t2.last_deal_price, -- 最近一次成交租金价格
    t2.last_deal_area, -- 最近一次成交的出租面积
    t2.last_deal_period, -- 最近一次成交的租赁期
    t2.last_deal_location, -- 最近一次成交项目的房屋坐落位置
    t2.last_deal_district, -- 最近一次成交项目的房屋所在省-市-区
    t2.last_deal_bizscop, -- 最近一次成交项目的房屋所在商圈
    t2.last_deal_address, -- 最近一次成交项目的房屋所在楼宇
    t2.his_bid_prj_cnt, -- 历史报价过的项目数量
    t2.his_bid_house_cate_cnt, -- 历史报价过的房屋类项目数量
    t2.his_bid_land_cate_cnt, -- 历史报价过的土地类项目数量
    t2.his_bid_district_most, -- 历史报价最多的房屋所在省-市-区
    t2.his_bid_bizscop_most, -- 历史报价最多的房屋所在商圈
    t2.last_bid_date, -- 最近一次报价的日期
    t2.last_bid_prj_nm, -- 最近一次报价的项目名称
    t2.last_bid_prj_id, -- 最近一次报价的项目编号
    t2.last_bid_asset_cate, -- 最近一次报价的资产类型
    t2.last_bid_prj_price, -- 最近一次报价的租金价格
    t2.last_bid_prj_total_price, -- 最近一次报价的租金总额
    t2.last_bid_district, -- 最近一次报价的地区_省-市-区
    t2.last_bid_bizscop, -- 最近一次报价的地区_商圈
    t2.his_bid_deal_rate, -- 历史报价成交率
    t2.his_bid_top3_num, -- 历史报价在项目总报价中排名前三的次数
    t2.his_bid_top3_rate -- 历史报价在项目总报价中排名前三的次数占比
FROM
    dws_wi_biz_houselease_lessee_d AS t1
LEFT JOIN
    AggregatedLesseeInfo AS t2 ON t1.intnt_rent_id = t2.intnt_rent_id;

